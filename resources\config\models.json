{"Chiller": {"P408": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P408", "asme_flag": false, "controls_parent": "503390"}, "P410": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P410", "asme_flag": false, "controls_parent": "503390"}, "P415": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P415", "asme_flag": false, "controls_parent": "503390"}, "P420": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P420", "asme_flag": false, "controls_parent": "503390"}, "P425": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P425", "asme_flag": false, "controls_parent": "503390"}, "P510": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P510", "asme_flag": false, "controls_parent": "503390"}, "P515": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P515", "asme_flag": false, "controls_parent": "503390"}, "P520": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P520", "asme_flag": false, "controls_parent": "503390"}, "P525": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P525", "asme_flag": false, "controls_parent": "503390"}, "P530": {"template_path": "resources/Templates/Chiller/Small Chiller Manual Template.dotx", "drawings_path": "resources/drawings/Small Chiller Drawings/P530", "asme_flag": false, "controls_parent": "503390"}}, "Paw Heater": {"PHP-1410-S": {"template_path": "resources/Templates/Paw Heater/Paw Heater Manual Template.dotx", "drawings_path": "resources/drawings/Paw Heater Drawings/Steam", "asme_flag": false, "controls_parent": "501254-S"}, "PHP-1410-E": {"template_path": "resources/Templates/Paw Heater/Paw Heater Manual Template.dotx", "drawings_path": "resources/drawings/Paw Heater Drawings/Electric", "asme_flag": false, "controls_parent": "501254"}}, "Pump": {"PSP-4": {"template_path": "resources/Templates/Pump/Pump Manual Template.dotx", "drawings_path": "resources/drawings/Pump Drawings", "asme_flag": false, "controls_parent": "500432"}, "PSP-6": {"template_path": "resources/Templates/Pump/Pump Manual Template.dotx", "drawings_path": "resources/drawings/Pump Drawings", "asme_flag": false, "controls_parent": "500433"}, "PSP-8": {"template_path": "resources/Templates/Pump/Pump Manual Template.dotx", "drawings_path": "resources/drawings/Pump Drawings", "asme_flag": false, "controls_parent": "500433"}}, "Marination Heat Exchanger": {"PHE-0610S": {"template_path": "resources/Templates/Marination Heat Exchanger/Marination Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Marination Heat Exchanger Drawings/PHE-0610S", "asme_flag": true, "controls_parent": "501281-M"}, "PHE-1010S": {"template_path": "resources/Templates/Marination Heat Exchanger/Marination Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Marination Heat Exchanger Drawings/PHE-1010S", "asme_flag": true, "controls_parent": "501281-M"}, "PHE-1010HS": {"template_path": "resources/Templates/Marination Heat Exchanger/Marination Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Marination Heat Exchanger Drawings/PHE-1010HS", "asme_flag": true, "controls_parent": "501281-M"}, "PHE-1010H": {"template_path": "resources/Templates/Marination Heat Exchanger/Marination Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Marination Heat Exchanger Drawings/PHE-1010H", "asme_flag": true, "controls_parent": "501281-M"}, "PHE-0820S": {"template_path": "resources/Templates/Marination Heat Exchanger/Marination Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Marination Heat Exchanger Drawings/PHE-0820S", "asme_flag": true, "controls_parent": "501281-M"}, "PHE-1220S": {"template_path": "resources/Templates/Marination Heat Exchanger/Marination Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Marination Heat Exchanger Drawings/PHE-1220S", "asme_flag": true, "controls_parent": "501281-M"}, "PHE-1220SX": {"template_path": "resources/Templates/Marination Heat Exchanger/Marination Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Marination Heat Exchanger Drawings/PHE-1220SX", "asme_flag": true, "controls_parent": "501281-M"}}, "Glycol Heat Exchanger": {"PHE-0610G": {"template_path": "resources/Templates/Glycol Heat Exchanger/Glycol Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Glycol Heat Exchanger Drawings/PHE-0610G", "asme_flag": true, "controls_parent": "501281-G"}, "PHE-1010G": {"template_path": "resources/Templates/Glycol Heat Exchanger/Glycol Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Glycol Heat Exchanger Drawings/PHE-1010G", "asme_flag": true, "controls_parent": "501281-G"}, "PHE-1010HSG": {"template_path": "resources/Templates/Glycol Heat Exchanger/Glycol Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Glycol Heat Exchanger Drawings/PHE-1010HSG", "asme_flag": true, "controls_parent": "501281-G"}}, "Heat Exchanger": {"PHI-110P": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PHI-110P", "asme_flag": true, "controls_parent": "501281"}, "PHI-135P": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PHI-135P", "asme_flag": true, "controls_parent": "501281"}, "PHI-130P": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PHI-130", "asme_flag": true, "controls_parent": "501281"}, "PHI-160P": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PHI-160P", "asme_flag": true, "controls_parent": "501281"}, "PHI-200P": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PHI-200P", "asme_flag": true, "controls_parent": "501281"}, "PHI-250P": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PHI-250P", "asme_flag": true, "controls_parent": "501281"}, "PHI-300P": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PHI-300P", "asme_flag": true, "controls_parent": "501281"}, "PGI-010": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGI-010", "asme_flag": true, "controls_parent": "501281"}, "PGI-016": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGI-016", "asme_flag": true, "controls_parent": "501281"}, "PGI-024": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGI-024", "asme_flag": true, "controls_parent": "501281"}, "PGI-060": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGI-060", "asme_flag": true, "controls_parent": "501281"}, "PGI-080": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGI-080", "asme_flag": true, "controls_parent": "501281"}, "PGE-0620": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGE-0620", "asme_flag": true, "controls_parent": "501281"}, "PGE-0820": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGE-0820", "asme_flag": true, "controls_parent": "501281"}, "PGE-1020": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGE-1020", "asme_flag": true, "controls_parent": "501281"}, "PGE-1220": {"template_path": "resources/Templates/Heat Exchanger/Heat Exchanger Manual Template.dotx", "drawings_path": "resources/drawings/Heat Exchanger Drawings/PGE-1220", "asme_flag": true, "controls_parent": "501281"}}, "Dual Mix Chil": {"DMC200J": {"template_path": "resources/Templates/Dual Mix Chil/Mixing System Jacketed Manual Template.dotx", "drawings_path": "resources/drawings/Dual Mix Chil Drawings/DMC200J", "asme_flag": true, "controls_parent": "501959"}, "DMC200H": {"template_path": "resources/Templates/Dual Mix Chil/Mixing System Non-Jackerted with HEX Manual Template.dotx", "drawings_path": "resources/drawings/Dual Mix Chil Drawings/DMC200H", "asme_flag": true, "controls_parent": "501959"}}, "Injector": {"Firebird 750": {"template_path": "resources/Templates/Injector/Fire Bird Standard Manual Template.dotx", "drawings_path": "resources/drawings/Injector/750 Standard", "asme_flag": true, "controls_parent": "504157"}, "Firebird 750FS": {"template_path": "resources/Templates/Injector/Fire Bird Food Service Manual.dotx", "drawings_path": "resources/drawings/Injector/750 Food Service", "asme_flag": true, "controls_parent": "503689"}, "Firebird 750DH": {"template_path": "resources/Templates/Injector/Fire Bird Dual Head Manual Template.dotx", "drawings_path": "resources/drawings/Injector/750 Dual Head", "asme_flag": true, "controls_parent": "504558"}, "Firebird 450": {"template_path": "resources/Templates/Injector/Fire Bird Standard Manual Template.dotx", "drawings_path": "resources/drawings/Injector/450 Standard", "asme_flag": true, "controls_parent": "504157"}, "Firebird 450FS": {"template_path": "resources/Templates/Injector/Fire Bird Food Service Manual.dotx", "drawings_path": "resources/drawings/Injector/450 Food Service", "asme_flag": true, "controls_parent": "503689"}, "Firebird 450DH": {"template_path": "resources/Templates/Injector/Fire Bird Dual Head Manual Template.dotx", "drawings_path": "resources/drawings/Injector/450 Dual Head", "asme_flag": true, "controls_parent": "504558"}}, "DeBoner": {"DSD-100": {"template_path": "resources/Templates/De-Boner/DSD-100_manual_template.dotx", "drawings_path": "resources/drawings/De-Boner/DSD-100", "asme_flag": false, "controls_parent": "505487"}}}