# Installation Guide

## System Requirements

- **Operating System**: Windows (required for E3.series)
- **E3.series**: Any recent version with COM API support
- **Python**: 3.7 or higher

## Step-by-Step Installation

### 1. Install Python
If you don't have Python installed:
1. Download Python from [python.org](https://www.python.org/downloads/)
2. During installation, make sure to check "Add Python to PATH"
3. Verify installation by opening Command Prompt and typing: `python --version`

### 2. Download E3 Automation Tools
1. Download the latest release from GitHub
2. Extract to a folder of your choice (e.g., `C:\E3-Tools\`)

### 3. Install Dependencies
1. Open Command Prompt as Administrator
2. Navigate to the extracted folder
3. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```

### 4. Verify Installation
1. Open E3.series and load a project
2. Open Command Prompt in the tools directory
3. Run a basic test:
   ```bash
   python scripts/set_wire_numbers.py
   ```
   (This will attempt to connect to E3 - make sure E3 is running with a project open)

## Troubleshooting

### "Failed to connect to E3"
- Ensure E3.series is running
- Verify a project is open in E3
- Try running Command Prompt as Administrator

### "Module not found" errors
- Verify Python is in your PATH
- Reinstall dependencies: `pip install -r requirements.txt`
- Check Python version: `python --version`

### COM Interface Issues
- Restart E3.series
- Restart your computer if COM registration issues persist
- Ensure no other applications are using E3's COM interface

## Getting Help

If you encounter issues:
1. Check the log files generated by the scripts
2. Review the troubleshooting section above
3. Open an issue on GitHub with:
   - Your Python version
   - Your E3.series version
   - The complete error message
   - The relevant log file contents
