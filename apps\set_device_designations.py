#!/usr/bin/env python3
"""
E3 Device Designation Automation Script

This script automatically updates device designations in E3.series projects based on the
sheet and grid position of the topmost leftmost pin of the first symbol of each device.

The designation format is: {device letter code}{sheet}.{grid}

For devices with conflicting designations, letter suffixes (A, B, C, etc.) are appended
similar to the wire numbering logic.

Special handling for terminal devices:
- Terminal devices (letter codes: T, TB, X, XT, TERM) do not get their designations renamed
- Instead, each pin of terminal devices gets its name set to the wire number from its connection

Author: <PERSON>
Date: 2025-07-07
Updated: 2025-07-08 - Migrated to use e3series PyPI package instead of win32com.client
"""

import logging
import sys
from typing import Dict, List, Tuple, Optional
import e3series

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Back to INFO level
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('device_designation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)


class DeviceDesignationManager:
    """Manages device designation automation for E3.series projects"""
    
    def __init__(self):
        self.app = None
        self.job = None
        self.device = None
        self.symbol = None
        self.sheet = None
        self.pin = None
        self.connection = None
        self.net_segment = None
        
    def connect_to_e3(self):
        """Connect to E3 application"""
        try:
            # Connect to the active E3.series application
            self.app = e3series.Application()
            self.job = self.app.CreateJobObject()
            self.device = self.job.CreateDeviceObject()
            self.symbol = self.job.CreateSymbolObject()
            self.sheet = self.job.CreateSheetObject()
            self.pin = self.job.CreatePinObject()
            self.connection = self.job.CreateConnectionObject()
            self.net_segment = self.job.CreateNetSegmentObject()

            logging.info("Successfully connected to E3 application using e3series library")
            return True

        except Exception as e:
            logging.error(f"Failed to connect to E3 application: {e}")
            return False
    
    def extract_grid_position(self, grid_desc: str) -> str:
        """
        Extract grid position from grid description string.
        
        Args:
            grid_desc: Grid description in format "/sheet.grid" or similar
            
        Returns:
            Grid position string (e.g., "A1", "B2")
        """
        if not grid_desc:
            return ""
            
        # Extract grid part after the last dot
        parts = grid_desc.split('.')
        if len(parts) >= 2:
            return parts[-1]
        
        return grid_desc
    
    def get_device_letter_code(self, device_id: int) -> str:
        """
        Get the device letter code from device attributes or name.
        
        Args:
            device_id: Device ID
            
        Returns:
            Device letter code (e.g., "M", "K", "T")
        """
        try:
            self.device.SetId(device_id)
            
            letercode = self.device.GetComponentAttributeValue("DeviceLetterCode")
            return letercode
            
            
        except Exception as e:
            logging.error(f"Error getting device letter code for device {device_id}: {e}")
            return "X"
    


    def generate_device_designation(self, letter_code: str, sheet: str, grid: str) -> str:
        """
        Generate device designation from components.

        Args:
            letter_code: Device letter code (e.g., "M", "K")
            sheet: Sheet page number
            grid: Grid position (e.g., "A1", "B2")

        Returns:
            Device designation string
        """
        return f"{letter_code}{sheet}{grid}"

    def get_first_symbol_info(self, device_id: int) -> Tuple[Optional[str], Optional[str], Optional[int]]:
        """
        Get the sheet, grid position, and first symbol ID of the topmost leftmost symbol.

        Args:
            device_id: Device ID

        Returns:
            Tuple of (sheet_assignment, grid_position, first_symbol_id) or (None, None, None) if not found
        """
        try:
            self.device.SetId(device_id)

            # Get all symbols for this device
            symbol_ids_result = self.device.GetSymbolIds()

            if not symbol_ids_result or symbol_ids_result == 0:
                symbol_ids_result = self.device.GetSymbolIds(1)  # 1 = placed symbols only

            if not symbol_ids_result or symbol_ids_result == 0:
                return None, None, None

            # Handle the result format - E3 returns (count, (symbol_id1, symbol_id2, ...))
            symbol_ids = []
            if isinstance(symbol_ids_result, tuple) and len(symbol_ids_result) >= 2:
                symbol_tuple = symbol_ids_result[1]

                if isinstance(symbol_tuple, tuple) and len(symbol_tuple) >= 1:
                    # Process all symbol IDs in the tuple
                    for sid in symbol_tuple:
                        if sid is not None and isinstance(sid, int) and sid > 0:
                            symbol_ids.append(sid)

            if not symbol_ids:
                return None, None, None

            best_position = None
            best_sheet = None
            best_symbol_id = None
            best_x = float('inf')
            best_y = float('inf')

            # Check each symbol to find the topmost leftmost position
            for symbol_id in symbol_ids:
                try:
                    if not isinstance(symbol_id, int) or symbol_id <= 0:
                        continue

                    self.symbol.SetId(symbol_id)
                    result = self.symbol.GetSchemaLocation()

                    if not result or result == 0:
                        continue

                    if isinstance(result, tuple) and len(result) >= 4:
                        sheet_id, x_pos, y_pos, grid_desc = result[:4]

                        if not isinstance(sheet_id, int) or sheet_id <= 0:
                            continue
                        if not isinstance(x_pos, (int, float)) or not isinstance(y_pos, (int, float)):
                            continue

                        # Get sheet assignment
                        self.sheet.SetId(sheet_id)
                        sheet_assignment = self.sheet.GetName()

                        grid_position = self.extract_grid_position(grid_desc)

                        # Check if this position is more topmost/leftmost
                        if (y_pos < best_y) or (y_pos == best_y and x_pos < best_x):
                            best_x = x_pos
                            best_y = y_pos
                            best_sheet = sheet_assignment
                            best_position = grid_position
                            best_symbol_id = symbol_id

                except Exception as e:
                    logging.debug(f"Error processing symbol {symbol_id} for device {device_id}: {e}")
                    continue

            if best_sheet and best_position and best_symbol_id:
                return best_sheet, best_position, best_symbol_id
            else:
                return None, None, None

        except Exception as e:
            logging.error(f"Error getting first symbol info for device {device_id}: {e}")
            return None, None, None

    def is_terminal_device(self, device_id: int) -> bool:
        """
        Check if a device is a terminal based on its letter code.

        Args:
            device_id: Device ID

        Returns:
            True if device is a terminal, False otherwise
        """
        try:
            letter_code = self.get_device_letter_code(device_id)
            # Common terminal letter codes
            terminal_codes = ['T', 'TB', 'X', 'XT', 'TERM']
            return letter_code in terminal_codes
        except Exception as e:
            logging.error(f"Error checking if device {device_id} is terminal: {e}")
            return False

    def get_wire_number_for_pin(self, pin_id: int) -> Optional[str]:
        """
        Get the wire number for a pin by finding its connection and net segment.

        Args:
            pin_id: Pin ID

        Returns:
            Wire number string or None if not found
        """
        try:
            # Get all connections to find which one contains this pin
            connection_ids_result = self.job.GetAllConnectionIds()
            if not connection_ids_result:
                return None

            actual_connections = []
            if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
                count = connection_ids_result[0]
                connection_ids = connection_ids_result[1]
                logging.debug(f"Found {count} connections to search for pin {pin_id}")

                if isinstance(connection_ids, tuple):
                    actual_connections = [cid for cid in connection_ids if cid is not None]
                else:
                    actual_connections = [connection_ids] if connection_ids is not None else []

            # Search through connections to find the one containing this pin
            for connection_id in actual_connections:
                try:
                    self.connection.SetId(connection_id)
                    pin_ids_result = self.connection.GetPinIds()

                    if pin_ids_result:
                        actual_pin_ids = []
                        if isinstance(pin_ids_result, tuple) and len(pin_ids_result) >= 2:
                            pin_ids = pin_ids_result[1]
                            if isinstance(pin_ids, tuple):
                                actual_pin_ids = [pid for pid in pin_ids if pid is not None and pid != 0]
                            else:
                                if pin_ids is not None and pin_ids != 0:
                                    actual_pin_ids = [pin_ids]

                        # Check if our pin is in this connection
                        if pin_id in actual_pin_ids:
                            # Get net segments for this connection
                            net_segment_ids_result = self.connection.GetNetSegmentIds()
                            if net_segment_ids_result:
                                actual_net_segments = []
                                if isinstance(net_segment_ids_result, tuple) and len(net_segment_ids_result) >= 2:
                                    net_segment_ids = net_segment_ids_result[1]
                                    if isinstance(net_segment_ids, tuple):
                                        actual_net_segments = [nsid for nsid in net_segment_ids if nsid is not None and nsid != 0]
                                    else:
                                        if net_segment_ids is not None and net_segment_ids != 0:
                                            actual_net_segments = [net_segment_ids]

                                # Get wire number from first net segment
                                for net_segment_id in actual_net_segments:
                                    try:
                                        self.net_segment.SetId(net_segment_id)
                                        wire_number = self.net_segment.GetAttributeValue("Wire number")
                                        if wire_number:
                                            return wire_number
                                    except Exception as e:
                                        logging.debug(f"Error getting wire number from net segment {net_segment_id}: {e}")
                                        continue

                            # If we found the connection but no wire number, return None
                            return None

                except Exception as e:
                    logging.debug(f"Error checking connection {connection_id} for pin {pin_id}: {e}")
                    continue

            return None

        except Exception as e:
            logging.error(f"Error getting wire number for pin {pin_id}: {e}")
            return None

    def update_terminal_pin_names(self, device_id: int) -> int:
        """
        Update pin names for a terminal device to match wire numbers.

        Args:
            device_id: Terminal device ID

        Returns:
            Number of pins successfully updated
        """
        try:
            self.device.SetId(device_id)

            # Get all pin IDs for this device
            pin_ids_result = self.device.GetPinIds()
            if not pin_ids_result:
                logging.debug(f"Terminal device {device_id} has no pins")
                return 0

            actual_pin_ids = []
            if isinstance(pin_ids_result, tuple) and len(pin_ids_result) >= 2:
                count = pin_ids_result[0]
                pin_ids = pin_ids_result[1]
                logging.debug(f"Terminal device {device_id} has {count} pins")

                if isinstance(pin_ids, tuple):
                    # Skip the first None element if present
                    for pid in pin_ids:
                        if pid is not None and isinstance(pid, int) and pid > 0:
                            actual_pin_ids.append(pid)
                else:
                    if pin_ids is not None and isinstance(pin_ids, int) and pin_ids > 0:
                        actual_pin_ids = [pin_ids]

            if not actual_pin_ids:
                logging.debug(f"No valid pin IDs found for terminal device {device_id}")
                return 0

            success_count = 0
            for pin_id in actual_pin_ids:
                try:
                    # Get wire number for this pin
                    wire_number = self.get_wire_number_for_pin(pin_id)

                    if wire_number:
                        # Set pin name to wire number
                        self.pin.SetId(pin_id)
                        result = self.pin.SetName(wire_number)
                        if result > 0:  # Success
                            logging.info(f"Updated pin {pin_id} of terminal {device_id} to wire number '{wire_number}'")
                            success_count += 1
                        else:
                            logging.warning(f"Failed to set pin {pin_id} name to '{wire_number}', result: {result}")
                    else:
                        logging.debug(f"No wire number found for pin {pin_id} of terminal {device_id}")

                except Exception as e:
                    logging.error(f"Error updating pin {pin_id} of terminal {device_id}: {e}")
                    continue

            return success_count

        except Exception as e:
            logging.error(f"Error updating terminal pin names for device {device_id}: {e}")
            return 0

    def assign_suffix_for_conflicts(self, designations: Dict[str, List[int]], device_data: Dict) -> Dict[int, str]:
        """
        Assign letter suffixes for conflicting device designations.

        Args:
            designations: Dictionary mapping base designation to list of device IDs
            device_data: Dictionary mapping device ID to device information including first_symbol_id

        Returns:
            Dictionary mapping device ID to final designation with suffix
        """
        final_designations = {}

        for base_designation, device_ids in designations.items():
            if len(device_ids) == 1:
                # No conflict, use base designation
                final_designations[device_ids[0]] = base_designation
            else:
                # Multiple devices with same base designation, sort by first symbol order and add suffixes
                logging.info(f"Found {len(device_ids)} devices with designation '{base_designation}', adding suffixes")

                # Sort devices by their first symbol order for consistent ordering
                device_orders = []
                for device_id in device_ids:
                    try:
                        # Use the first symbol ID that was already identified for this device
                        if device_id in device_data and 'first_symbol_id' in device_data[device_id]:
                            first_symbol_id = device_data[device_id]['first_symbol_id']

                            try:
                                self.symbol.SetId(first_symbol_id)
                                symbol_order = self.symbol.GetSymbolOrder()
                                location = self.symbol.GetSchemaLocation()
                                if len(location) >= 3:
                                    x_pos = location[1]
                                    logging.debug(f"Device {device_id} first symbol {first_symbol_id}: order={symbol_order}, x_pos={x_pos}")
                                    device_orders.append((symbol_order, x_pos, device_id))
                                else:
                                    logging.debug(f"Could not get location for first symbol {first_symbol_id} of device {device_id}")
                                    device_orders.append((float('inf'), 0.0, device_id))
                            except Exception as e:
                                logging.debug(f"Could not get order for first symbol {first_symbol_id} of device {device_id}: {e}")
                                # Try to get position as fallback
                                try:
                                    self.symbol.SetId(first_symbol_id)
                                    location = self.symbol.GetSchemaLocation()
                                    if len(location) >= 3:
                                        x_pos = location[1]
                                        # Use a high order value for symbols without order
                                        fallback_order = 9999
                                        device_orders.append((fallback_order, x_pos, device_id))
                                    else:
                                        device_orders.append((float('inf'), 0.0, device_id))
                                except Exception as e2:
                                    logging.debug(f"Could not get position for first symbol {first_symbol_id} of device {device_id}: {e2}")
                                    device_orders.append((float('inf'), 0.0, device_id))
                        else:
                            logging.debug(f"No first symbol ID found for device {device_id}")
                            device_orders.append((float('inf'), 0.0, device_id))
                    except Exception as e:
                        logging.debug(f"Could not get order/position for device {device_id}: {e}")
                        # If we can't get order or position, add with default values
                        device_orders.append((float('inf'), 0.0, device_id))

                # Sort by symbol order first, then by x position
                device_orders.sort(key=lambda x: (x[0], x[1]))

                # Assign suffixes - first device keeps original designation, others get suffixes
                for i, (_, _, device_id) in enumerate(device_orders):
                    if i == 0:
                        # First device keeps the original designation (no suffix)
                        final_designations[device_id] = base_designation
                        logging.info(f"Device {device_id} assigned designation: {base_designation} (first device, no suffix)")
                    else:
                        # Subsequent devices get suffixes starting with A
                        suffix = chr(ord('A') + i - 1)  # i-1 so second device gets 'A', third gets 'B', etc.
                        final_designation = f"{base_designation}.{suffix}"
                        final_designations[device_id] = final_designation
                        logging.info(f"Device {device_id} assigned designation: {final_designation}")

        return final_designations

    def update_device_designation(self, device_id: int, designation: str) -> bool:
        """
        Update the device designation attribute.

        Args:
            device_id: Device ID
            designation: New designation

        Returns:
            True if successful, False otherwise
        """
        try:
            self.device.SetId(device_id)

            # Use SetName() to set the device designation
            result = self.device.SetName(designation)
            if result > 0:  # Success
                logging.info(f"Updated device {device_id} designation to '{designation}' using SetName()")
                return True
            else:
                logging.warning(f"SetName() failed for device {device_id}, result: {result}")
                return False

        except Exception as e:
            logging.error(f"Error updating designation for device {device_id}: {e}")
            return False

    def get_all_device_and_cable_ids(self):
        """
        Get all device IDs and cable IDs from the project.

        Returns:
            List of all device and cable IDs
        """
        all_devices = []

        # Get regular devices
        try:
            device_ids_result = self.job.GetAllDeviceIds()
            if device_ids_result:
                if isinstance(device_ids_result, tuple) and len(device_ids_result) >= 2:
                    count = device_ids_result[0]
                    device_ids = device_ids_result[1]
                    logging.info(f"E3 reports {count} regular devices")

                    if isinstance(device_ids, tuple):
                        # Filter out None values
                        regular_devices = [did for did in device_ids if did is not None]
                        all_devices.extend(regular_devices)
                    else:
                        if device_ids is not None:
                            all_devices.append(device_ids)
                else:
                    logging.warning(f"Unexpected device IDs format: {type(device_ids_result)}")
        except Exception as e:
            logging.error(f"Error getting regular device IDs: {e}")

        # Get cables
        try:
            cable_ids_result = self.job.GetCableIds()
            if cable_ids_result:
                if isinstance(cable_ids_result, tuple) and len(cable_ids_result) >= 2:
                    count = cable_ids_result[0]
                    cable_ids = cable_ids_result[1]
                    logging.info(f"E3 reports {count} cables")

                    if isinstance(cable_ids, tuple):
                        # Filter out None values
                        cables = [cid for cid in cable_ids if cid is not None]
                        all_devices.extend(cables)
                    else:
                        if cable_ids is not None:
                            all_devices.append(cable_ids)
                else:
                    logging.warning(f"Unexpected cable IDs format: {type(cable_ids_result)}")
        except Exception as e:
            logging.error(f"Error getting cable IDs: {e}")

        return all_devices

    def is_cable_device(self, device_id: int) -> bool:
        """
        Check if a device is a cable using the E3 API.

        Args:
            device_id: Device ID

        Returns:
            True if device is a cable, False otherwise
        """
        try:
            self.device.SetId(device_id)
            result = self.device.IsCable()
            return result == 1
        except Exception as e:
            logging.debug(f"Error checking if device {device_id} is cable: {e}")
            return False

    def process_devices(self):
        """Process all devices and cables in the project"""
        try:
            # Get all device IDs (including cables)
            actual_devices = self.get_all_device_and_cable_ids()

            if not actual_devices:
                logging.warning("No devices or cables found in project")
                return

            logging.info(f"Processing {len(actual_devices)} devices and cables")

            # Collect device data
            device_data = {}
            designations = {}  # base_designation -> [device_ids]
            devices_with_symbols = 0
            devices_without_symbols = 0
            terminal_devices = []
            non_terminal_devices = []

            # First pass: separate terminals from other devices and identify cables
            cable_count = 0
            for device_id in actual_devices:
                try:
                    # Check if this is a cable
                    if self.is_cable_device(device_id):
                        cable_count += 1
                        logging.info(f"Device {device_id} identified as cable")
                        # Cables are processed as non-terminal devices for designation purposes
                        non_terminal_devices.append(device_id)
                    elif self.is_terminal_device(device_id):
                        terminal_devices.append(device_id)
                        logging.info(f"Device {device_id} identified as terminal")
                    else:
                        non_terminal_devices.append(device_id)
                except Exception as e:
                    logging.error(f"Error checking device type for {device_id}: {e}")
                    non_terminal_devices.append(device_id)  # Default to non-terminal

            logging.info(f"Found {len(terminal_devices)} terminal devices, {len(non_terminal_devices)} non-terminal devices (including {cable_count} cables)")

            # Process non-terminal devices for designation updates
            for device_id in non_terminal_devices:
                try:
                    # Get device letter code
                    letter_code = self.get_device_letter_code(device_id)

                    # Get position and first symbol ID of topmost leftmost symbol
                    sheet, grid, first_symbol_id = self.get_first_symbol_info(device_id)

                    if sheet and grid and first_symbol_id:
                        # Generate base designation
                        base_designation = self.generate_device_designation(letter_code, sheet, grid)

                        # Store device data including the first symbol ID
                        device_data[device_id] = {
                            'letter_code': letter_code,
                            'sheet': sheet,
                            'grid': grid,
                            'base_designation': base_designation,
                            'first_symbol_id': first_symbol_id
                        }

                        # Track designations for conflict resolution
                        if base_designation not in designations:
                            designations[base_designation] = []
                        designations[base_designation].append(device_id)

                        logging.info(f"Device {device_id}: {letter_code} at sheet {sheet}, grid {grid} -> {base_designation}")
                        devices_with_symbols += 1
                    else:
                        logging.info(f"Could not determine position for device {device_id} (sheet={sheet}, grid={grid}, symbol_id={first_symbol_id}), counting as without symbols")
                        devices_without_symbols += 1

                except Exception as e:
                    logging.error(f"Error processing device {device_id}: {e}")
                    devices_without_symbols += 1
                    continue

            logging.info(f"Found {devices_with_symbols} non-terminal devices with placed symbols, {devices_without_symbols} devices without placed symbols")

            # Resolve conflicts and assign final designations for non-terminal devices
            final_designations = self.assign_suffix_for_conflicts(designations, device_data)

            # Update device designations for non-terminal devices
            designation_success_count = 0
            for device_id, final_designation in final_designations.items():
                if self.update_device_designation(device_id, final_designation):
                    designation_success_count += 1

            logging.info(f"Successfully updated {designation_success_count} out of {len(final_designations)} device designations")

            # Process terminal devices for pin name updates
            terminal_pin_success_count = 0
            total_terminal_pins = 0
            for device_id in terminal_devices:
                try:
                    pins_updated = self.update_terminal_pin_names(device_id)
                    terminal_pin_success_count += pins_updated
                    if pins_updated > 0:
                        total_terminal_pins += pins_updated
                        logging.info(f"Updated {pins_updated} pin names for terminal device {device_id}")
                    else:
                        logging.debug(f"No pins updated for terminal device {device_id}")
                except Exception as e:
                    logging.error(f"Error updating terminal pin names for device {device_id}: {e}")
                    continue

            logging.info(f"Successfully updated {terminal_pin_success_count} pin names across {len(terminal_devices)} terminal devices")

        except Exception as e:
            logging.error(f"Error in process_devices: {e}")

    def run(self):
        """Main execution method"""
        try:
            logging.info("Starting E3 Device Designation Automation")

            if not self.connect_to_e3():
                return False

            self.process_devices()

            logging.info("Device designation automation completed")
            return True

        except Exception as e:
            logging.error(f"Error in main execution: {e}")
            return False
        finally:
            # Clean up E3.series objects
            self.app = None
            self.job = None
            self.device = None
            self.symbol = None
            self.sheet = None
            self.pin = None
            self.connection = None
            self.net_segment = None


def main():
    """Main entry point"""
    try:
        manager = DeviceDesignationManager()
        success = manager.run()

        if success:
            print("Device designation automation completed successfully!")
            return 0
        else:
            print("Device designation automation failed!")
            return 1

    except Exception as e:
        logging.error(f"Unexpected error in main: {e}")
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
