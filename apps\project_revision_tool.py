#!/usr/bin/env python3
"""
Project Revision Tool

This application provides a GUI to create project revisions by moving the current project 
to a subfolder and creating a new version with an incremented REV# in the filename.
"""

import os
import sys
import shutil
import logging
import traceback
import re
from datetime import datetime
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import utility functions if available
try:
    from utils import ensure_dir_exists, setup_logging
    from lib.theme_utils import apply_theme
except ImportError:
    try:
        from lib.utils import ensure_dir_exists, setup_logging
        from lib.theme_utils import apply_theme
    except ImportError:
        # Define basic utility functions if import fails
        def ensure_dir_exists(directory):
            """Ensure a directory exists, creating it if necessary"""
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            return directory

        def setup_logging(log_name):
            """Set up basic logging to file and console"""
            log_dir = ensure_dir_exists(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs'))
            log_file = os.path.join(log_dir, f"{log_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

            logging.basicConfig(
                level=logging.INFO,
                format="%(asctime)s [%(levelname)s] %(message)s",
                handlers=[
                    logging.FileHandler(log_file),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            return log_file

        # Define a basic theme utility if import fails
        def apply_theme(theme_name="red", appearance_mode="dark"):
            ctk.set_appearance_mode(appearance_mode)
            ctk.set_default_color_theme("blue")

class StatusLogger:
    """Simple logger that updates a status label in the UI."""

    def __init__(self, status_label):
        self.status_label = status_label

    def info(self, message):
        """Log an info message and update status."""
        logging.info(message)
        self.status_label.configure(text=message, text_color="#2AA876")  # Green
        self.status_label.update()

    def warning(self, message):
        """Log a warning message and update status."""
        logging.warning(message)
        self.status_label.configure(text=message, text_color="#FFA500")  # Orange
        self.status_label.update()

    def error(self, message):
        """Log an error message and update status."""
        logging.error(message)
        self.status_label.configure(text=message, text_color="#FF0000")  # Red
        self.status_label.update()

    def exception(self, e, message="An error occurred"):
        """Log an exception and update status."""
        logging.exception(f"{message}: {str(e)}")
        self.status_label.configure(text=f"{message}: {str(e)}", text_color="#FF0000")  # Red
        self.status_label.update()

class ProjectRevisionTool(ctk.CTk):
    """Application for creating project revisions with automatic backup and versioning."""

    def __init__(self):
        """Initialize the application."""
        super().__init__()

        # Set up the window
        self.title("Project Revision Tool")
        self.geometry("900x700")
        self.minsize(700, 500)

        # Initialize variables
        self.current_project_path = None
        self.project_directory = None
        self.project_filename = None
        self.is_directory = False

        # Create widgets
        self.create_widgets()

        # Set up status logger
        self.status_logger = StatusLogger(self.status_label)

        # Log application start
        self.status_logger.info("Application started")

    def create_widgets(self):
        """Create the widgets for the application window."""
        # Configure grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)

        # Create project selection frame
        self.selection_frame = ctk.CTkFrame(self)
        self.selection_frame.grid(row=0, column=0, padx=20, pady=10, sticky="ew")
        self.selection_frame.grid_columnconfigure(1, weight=1)

        # File selection button
        self.btn_select_file = ctk.CTkButton(
            self.selection_frame,
            text="Select Project File",
            command=self.select_project_file,
            width=140,
            corner_radius=8
        )
        self.btn_select_file.grid(row=0, column=0, padx=5, pady=10)

        # Directory selection button
        self.btn_select_dir = ctk.CTkButton(
            self.selection_frame,
            text="Select Project Directory",
            command=self.select_project_directory,
            width=140,
            corner_radius=8
        )
        self.btn_select_dir.grid(row=1, column=0, padx=5, pady=5)

        # Selected path label
        self.lbl_selected_path = ctk.CTkLabel(
            self.selection_frame,
            text="No project selected",
            anchor="w",
            wraplength=600
        )
        self.lbl_selected_path.grid(row=0, column=1, rowspan=2, padx=10, pady=10, sticky="ew")

        # Create main content frame
        self.content_frame = ctk.CTkFrame(self)
        self.content_frame.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        self.content_frame.grid_columnconfigure(0, weight=1)
        self.content_frame.grid_rowconfigure(1, weight=1)

        # Create results label
        self.results_label = ctk.CTkLabel(
            self.content_frame,
            text="Revision Process Log",
            font=("Arial", 14, "bold")
        )
        self.results_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")

        # Create scrollable text area for results
        self.results_text = ctk.CTkTextbox(
            self.content_frame,
            wrap="word",
            font=("Courier New", 11),
            text_color="white"
        )
        self.results_text.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")

        # Create button frame
        self.button_frame = ctk.CTkFrame(self)
        self.button_frame.grid(row=2, column=0, padx=20, pady=10, sticky="ew")

        self.btn_create_revision = ctk.CTkButton(
            self.button_frame,
            text="Create Revision",
            command=self.create_revision,
            width=150,
            height=40,
            corner_radius=8,
            fg_color="#C53F3F",  # Red color
            hover_color="#A02222",  # Darker red
            state="disabled"  # Initially disabled
        )
        self.btn_create_revision.pack(side="right", padx=5, pady=10)

        # Create status bar
        self.status_label = ctk.CTkLabel(
            self,
            text="Ready - Select a project file or directory to begin",
            anchor="w",
            height=25
        )
        self.status_label.grid(row=3, column=0, padx=20, pady=5, sticky="ew")

    def select_project_file(self):
        """Open a file selection dialog for project files."""
        file_path = filedialog.askopenfilename(
            title="Select Project File",
            filetypes=[
                ("All Files", "*.*"),
                ("CAD Files", "*.dwg;*.dxf"),
                ("Document Files", "*.docx;*.pdf"),
                ("Project Files", "*.prj;*.proj")
            ]
        )
        if file_path:
            self.set_project_path(file_path, is_directory=False)

    def select_project_directory(self):
        """Open a directory selection dialog for project directories."""
        directory = filedialog.askdirectory(title="Select Project Directory")
        if directory:
            self.set_project_path(directory, is_directory=True)

    def set_project_path(self, path, is_directory):
        """Set the current project path and update UI."""
        self.current_project_path = path
        self.is_directory = is_directory
        
        if is_directory:
            self.project_directory = os.path.dirname(path)
            self.project_filename = os.path.basename(path)
        else:
            self.project_directory = os.path.dirname(path)
            self.project_filename = os.path.basename(path)
        
        # Update UI
        self.lbl_selected_path.configure(text=path)
        self.btn_create_revision.configure(state="normal")
        
        # Clear previous results
        self.results_text.delete("1.0", tk.END)
        
        # Log selection
        project_type = "directory" if is_directory else "file"
        self.status_logger.info(f"Selected project {project_type}: {os.path.basename(path)}")
        self.results_text.insert(tk.END, f"Selected project {project_type}: {path}\n")
        
        # Analyze current revision
        self.analyze_current_revision()

    def analyze_current_revision(self):
        """Analyze the current project to determine revision information."""
        if not self.current_project_path:
            return
            
        filename = self.project_filename
        current_rev = self.extract_revision_number(filename)
        
        if current_rev is not None:
            self.results_text.insert(tk.END, f"Current revision detected: REV{current_rev}\n")
            next_rev = current_rev + 1
            self.results_text.insert(tk.END, f"Next revision will be: REV{next_rev}\n")
        else:
            self.results_text.insert(tk.END, "No revision number detected in filename\n")
            self.results_text.insert(tk.END, "Next revision will be: REV1\n")

    def extract_revision_number(self, filename):
        """Extract revision number from filename. Returns None if no revision found."""
        # Look for REV followed by digits (case insensitive)
        pattern = r'REV(\d+)'
        match = re.search(pattern, filename, re.IGNORECASE)
        if match:
            return int(match.group(1))
        return None

    def generate_new_filename(self, original_filename):
        """Generate new filename with incremented revision number."""
        current_rev = self.extract_revision_number(original_filename)
        
        if current_rev is not None:
            # Replace existing REV# with incremented version
            pattern = r'REV\d+'
            new_rev = f"REV{current_rev + 1}"
            new_filename = re.sub(pattern, new_rev, original_filename, flags=re.IGNORECASE)
        else:
            # Add REV1 to filename
            name, ext = os.path.splitext(original_filename)
            new_filename = f"{name}_REV1{ext}"
        
        return new_filename

    def create_revision(self):
        """Create a new revision by backing up current project and creating new version."""
        if not self.current_project_path:
            messagebox.showerror("Error", "No project selected")
            return

        try:
            self.results_text.delete("1.0", tk.END)
            self.results_text.insert(tk.END, "Starting revision process...\n\n")

            # Step 1: Create revisions subfolder
            revisions_folder = os.path.join(self.project_directory, "revisions")
            ensure_dir_exists(revisions_folder)
            self.results_text.insert(tk.END, f"Created/verified revisions folder: {revisions_folder}\n")

            # Step 2: Generate backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if self.is_directory:
                backup_name = f"{self.project_filename}_{timestamp}"
                backup_path = os.path.join(revisions_folder, backup_name)
            else:
                name, ext = os.path.splitext(self.project_filename)
                backup_name = f"{name}_{timestamp}{ext}"
                backup_path = os.path.join(revisions_folder, backup_name)

            # Step 3: Move current project to revisions folder
            self.status_logger.info("Moving current project to revisions folder...")
            if self.is_directory:
                shutil.move(self.current_project_path, backup_path)
                self.results_text.insert(tk.END, f"Moved directory to: {backup_path}\n")
            else:
                shutil.move(self.current_project_path, backup_path)
                self.results_text.insert(tk.END, f"Moved file to: {backup_path}\n")

            # Step 4: Generate new filename with incremented revision
            new_filename = self.generate_new_filename(self.project_filename)
            new_project_path = os.path.join(self.project_directory, new_filename)

            # Step 5: Copy backup back to original location with new name
            self.status_logger.info("Creating new revision...")
            if self.is_directory:
                shutil.copytree(backup_path, new_project_path)
                self.results_text.insert(tk.END, f"Created new revision directory: {new_filename}\n")
            else:
                shutil.copy2(backup_path, new_project_path)
                self.results_text.insert(tk.END, f"Created new revision file: {new_filename}\n")

            # Step 6: Update current project path to new revision
            self.current_project_path = new_project_path
            self.project_filename = new_filename
            self.lbl_selected_path.configure(text=new_project_path)

            # Success message
            self.results_text.insert(tk.END, f"\n✓ Revision created successfully!\n")
            self.results_text.insert(tk.END, f"✓ Original backed up to: {os.path.relpath(backup_path, self.project_directory)}\n")
            self.results_text.insert(tk.END, f"✓ New revision: {new_filename}\n")

            self.status_logger.info("Revision created successfully")

            # Ask if user wants to open the project directory
            if messagebox.askyesno("Revision Complete",
                                 f"Revision created successfully!\n\n"
                                 f"New revision: {new_filename}\n"
                                 f"Backup saved in revisions folder\n\n"
                                 f"Open project directory?"):
                os.startfile(self.project_directory)

        except Exception as e:
            self.status_logger.exception(e, "Error creating revision")
            messagebox.showerror("Error", f"Failed to create revision: {str(e)}")

def main():
    """Main entry point for the application."""
    try:
        # Set up logging
        log_file = setup_logging("project_revision_tool")
        logging.info("Starting Project Revision Tool")

        # Apply the red theme
        apply_theme("red", "dark")

        # Create and run the application
        app = ProjectRevisionTool()
        app.mainloop()

        logging.info("Application closed")
    except Exception as e:
        logging.error(f"Unhandled exception: {e}")
        logging.debug(traceback.format_exc())
        print(f"An error occurred: {e}")
        print(f"See log file for details: {log_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
